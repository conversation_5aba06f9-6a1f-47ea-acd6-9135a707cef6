"""
Deepgram Client for Audio Timing Analysis
-----------------------------------------
Provides audio timing analysis using Deepgram's Whisper model for Hindi narration.
This utility is used to calculate precise narration duration for video generation
by analyzing STT-generated audio files and extracting word-level timing information.

Features:
- Transcribes Hindi audio files using Deepgram's Whisper model
- Extracts word-level timing information
- Calculates precise narration duration based on actual audio content
- Supports both local audio files and audio URLs
"""

import os
import logging
from typing import Dict, List, Optional, Tuple
from deepgram import DeepgramClient, PrerecordedOptions

logger = logging.getLogger(__name__)


class DeepgramTimingClient:
    """
    Client for analyzing audio timing using Deepgram's Whisper model.
    
    This client is specifically designed for Hindi narration timing analysis
    in the video generation pipeline.
    """

    def __init__(self):
        """
        Initialize the Deepgram client with API key from environment.
        
        Raises:
            ValueError: If DEEPGRAM_API_KEY is not found in environment variables.
        """
        self.api_key = os.getenv("DEEPGRAM_API_KEY")
        
        if not self.api_key:
            raise ValueError("Missing required DEEPGRAM_API_KEY environment variable")
        
        self.client = DeepgramClient(self.api_key)
        logger.info("Initialized Deepgram timing client")

    def analyze_audio_timing(self, audio_file_path: str) -> Dict:
        """
        Analyze audio file to extract timing information using Deepgram's Whisper model.
        
        Args:
            audio_file_path (str): Path to the audio file to analyze
            
        Returns:
            Dict: Complete Deepgram response with timing information
            
        Raises:
            FileNotFoundError: If the audio file doesn't exist
            Exception: If Deepgram API call fails
        """
        if not os.path.exists(audio_file_path):
            raise FileNotFoundError(f"Audio file not found: {audio_file_path}")
        
        try:
            # Configure Deepgram options for Hindi transcription
            options = PrerecordedOptions(
                model="whisper",
                language="hi",  # Hindi language
                smart_format=True,
                punctuate=True,
                diarize=False,
                utterances=False,
                paragraphs=True,
                summarize=False,
                detect_language=False,
                search=None,
                replace=None,
                keywords=None,
                profanity_filter=False,
                redact=None,
                alternatives=1,
                numerals=False,
                measurements=False,
                dates=False,
                times=False,
                currencies=False,
                tag=None
            )

            # Transcribe the audio file
            with open(audio_file_path, "rb") as audio_file:
                response = self.client.listen.rest.v("1").transcribe_file(
                    source=audio_file,
                    options=options
                )

            logger.info(f"Successfully analyzed audio timing for: {audio_file_path}")
            return response.to_dict()

        except Exception as e:
            logger.error(f"Error analyzing audio timing for {audio_file_path}: {str(e)}")
            raise

    def calculate_narration_duration(self, audio_file_path: str, narration_text: str) -> float:
        """
        Calculate the duration of specific narration text within an audio file.
        
        This method analyzes the audio file, extracts word-level timing, and calculates
        the duration by matching words from the narration text with the transcribed words.
        
        Args:
            audio_file_path (str): Path to the audio file
            narration_text (str): The narration text to calculate duration for
            
        Returns:
            float: Duration in seconds for the narration text
        """
        try:
            # Get timing analysis from Deepgram
            response = self.analyze_audio_timing(audio_file_path)
            
            # Extract words with timing information
            words_with_timing = self._extract_words_with_timing(response)
            
            if not words_with_timing:
                logger.warning(f"No words with timing found in audio file: {audio_file_path}")
                return 0.0

            # Calculate duration based on word matching
            duration = self._calculate_duration_from_words(narration_text, words_with_timing)

            logger.info(f"Calculated narration duration: {duration:.2f} seconds for text: {narration_text[:50]}...")
            return duration

        except Exception as e:
            logger.error(f"Error calculating narration duration: {str(e)}")
            return 0.0

    def _extract_words_with_timing(self, response: Dict) -> List[Dict]:
        """
        Extract words with timing information from Deepgram response.
        
        Args:
            response (Dict): Deepgram API response
            
        Returns:
            List[Dict]: List of words with timing information
        """
        try:
            # Navigate through the response structure
            results = response.get("results", {})
            channels = results.get("channels", [])
            
            if not channels:
                return []
            
            alternatives = channels[0].get("alternatives", [])
            
            if not alternatives:
                return []
            
            words = alternatives[0].get("words", [])
            return words

        except (KeyError, IndexError) as e:
            logger.error(f"Error extracting words from Deepgram response: {str(e)}")
            return []

    def _calculate_duration_from_words(self, narration_text: str, words_with_timing: List[Dict]) -> float:
        """
        Calculate duration by matching narration words with transcribed words.
        
        Args:
            narration_text (str): The narration text
            words_with_timing (List[Dict]): Words with timing from Deepgram
            
        Returns:
            float: Calculated duration in seconds
        """
        # Split narration into words (remove punctuation and convert to lowercase)
        narration_words = self._clean_and_split_text(narration_text)
        
        if not narration_words:
            return 0.0
        
        # Find matching words and their timing
        matched_timings = []
        
        for narration_word in narration_words:
            # Find the best matching word in the transcribed words
            best_match = self._find_best_word_match(narration_word, words_with_timing)
            if best_match:
                matched_timings.append(best_match)
        
        if not matched_timings:
            logger.warning("No matching words found between narration and transcription")
            return 0.0

        # Calculate duration from first to last matched word
        start_time = min(word.get("start", 0) for word in matched_timings)
        end_time = max(word.get("end", 0) for word in matched_timings)

        duration = end_time - start_time

        return duration

    def _clean_and_split_text(self, text: str) -> List[str]:
        """
        Clean text and split into words for matching.
        
        Args:
            text (str): Input text
            
        Returns:
            List[str]: List of cleaned words
        """
        import re
        
        # Remove punctuation and extra whitespace
        cleaned_text = re.sub(r'[^\w\s]', ' ', text)
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
        
        # Split into words and convert to lowercase
        words = [word.lower() for word in cleaned_text.split() if word.strip()]
        
        return words

    def _find_best_word_match(self, narration_word: str, words_with_timing: List[Dict]) -> Optional[Dict]:
        """
        Find the best matching word in the transcribed words.
        
        Args:
            narration_word (str): Word from narration text
            words_with_timing (List[Dict]): Transcribed words with timing
            
        Returns:
            Optional[Dict]: Best matching word with timing, or None if no match
        """
        narration_word_lower = narration_word.lower()
        
        # First, try exact match
        for word_data in words_with_timing:
            transcribed_word = word_data.get("word", "").lower()
            if transcribed_word == narration_word_lower:
                return word_data
        
        # Then, try partial match (word contains narration word or vice versa)
        for word_data in words_with_timing:
            transcribed_word = word_data.get("word", "").lower()
            if (narration_word_lower in transcribed_word or 
                transcribed_word in narration_word_lower):
                return word_data
        
        return None

    def get_total_audio_duration(self, audio_file_path: str) -> float:
        """
        Get the total duration of an audio file.

        Args:
            audio_file_path (str): Path to the audio file

        Returns:
            float: Total duration in seconds
        """
        try:
            response = self.analyze_audio_timing(audio_file_path)

            # Extract duration from metadata
            metadata = response.get("metadata", {})
            duration = metadata.get("duration", 0.0)

            if duration > 0:
                return duration

            # Fallback: calculate from words timing
            words_with_timing = self._extract_words_with_timing(response)
            if words_with_timing:
                last_word = max(words_with_timing, key=lambda w: w.get("end", 0))
                return last_word.get("end", 0.0)

            return 0.0

        except Exception as e:
            logger.error(f"Error getting audio duration: {str(e)}")
            return 0.0


def create_deepgram_client() -> DeepgramTimingClient:
    """
    Factory function to create a Deepgram timing client.

    Returns:
        DeepgramTimingClient: Configured Deepgram client instance
    """
    return DeepgramTimingClient()
