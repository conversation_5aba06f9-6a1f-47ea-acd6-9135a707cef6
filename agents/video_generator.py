"""
Video Generator Agent
--------------------
Generates video content for story segments using Replicate's bytedance/seedance-1-lite model.
Implements a two-phase approach: visual planning followed by video generation.
"""

import os
import logging
import requests
from typing import List, Dict

import replicate
from moviepy.video.io.VideoFileClip import VideoFileClip

from models.schema import VisualSegment
from inference.image_generator import ImageGenerator
from agents.visual_planner import VisualPlanningAgent
from utils.shorts_utils import get_shorts_directories
from utils.deepgram_client import create_deepgram_client

logger = logging.getLogger(__name__)


class VideoGeneratorAgent:
    """
    Agent for generating video content from story segments using Replicate's bytedance/seedance-1-lite model.

    This agent implements a two-phase approach:
    1. Visual Planning: Creates comprehensive visual plans for entire shorts
    2. Video Generation: Generates individual videos using the planned visual prompts
    """

    def __init__(self, verbose: bool = False, model: str = "gpt-4o-mini", provider: str = "openai", video_resolution: str = "480p"):
        """
        Initialize the Video Generator Agent.

        Args:
            verbose (bool): Enable verbose output from CrewAI agents
            model (str): Model name to use for LLM
            provider (str): Provider to use for LLM
            video_resolution (str): Video resolution (480p or 720p)
        """
        self.verbose = verbose
        self.model = model
        self.provider = provider
        self.video_resolution = video_resolution

        # Validate Replicate API key
        self.replicate_api_key = os.getenv("REPLICATE_API_KEY")
        if not self.replicate_api_key:
            raise ValueError("Missing required REPLICATE_API_KEY for VideoGeneratorAgent")

        # Configure replicate client
        os.environ['REPLICATE_API_TOKEN'] = self.replicate_api_key

        # Initialize the visual planning agent
        self.visual_planner = VisualPlanningAgent(
            verbose=verbose,
            model=model,
            provider=provider
        )

        # Initialize the image generator
        self.image_generator = ImageGenerator(self.replicate_api_key)

        # Initialize the Deepgram client for audio timing analysis
        try:
            self.deepgram_client = create_deepgram_client()
            logger.info("Initialized Deepgram client for audio timing analysis")
        except Exception as e:
            logger.warning(f"Failed to initialize Deepgram client: {str(e)}. Will return 0.0 duration.")
            self.deepgram_client = None


    def generate_video(self, visual_segment: VisualSegment, output_path: str, short_dir: str) -> bool:
        """
        Generate video using Replicate's bytedance/seedance-1-lite model with frame control.

        Args:
            visual_segment (VisualSegment): The visual segment containing prompt and frame control settings
            output_path (str): Path to save the generated video
            short_dir (str): Path to the short directory for image generation

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Generate start and end images if needed
            start_image_path = None
            end_image_path = None

            if visual_segment.start_with_image:
                start_image_path = self.image_generator.generate_start_image(visual_segment, short_dir)
                if not start_image_path:
                    logger.warning(f"Failed to generate start image for segment {visual_segment.segment_number}")

            if visual_segment.end_with_image:
                end_image_path = self.image_generator.generate_end_image(visual_segment, short_dir)
                if not end_image_path:
                    logger.warning(f"Failed to generate end image for segment {visual_segment.segment_number}")

            # Handle previous frame continuation
            if visual_segment.start_with_prev_frame:
                prev_frame_path = self.image_generator.get_previous_end_frame(visual_segment.segment_number, short_dir)
                if prev_frame_path:
                    start_image_path = prev_frame_path
                    logger.info(f"Using previous end frame as start image: {prev_frame_path}")
                else:
                    logger.warning(f"Previous end frame not found for segment {visual_segment.segment_number}")

            # Prepare input parameters for bytedance/seedance-1-lite
            input_params = {
                "prompt": visual_segment.visual_prompt,
                "duration": 5,  # Always generate 5-second clips
                "resolution": self.video_resolution,
                "aspect_ratio": "9:16"  # Vertical format for shorts
            }

            # Add start image if available
            if start_image_path:
                # Upload start image to a temporary URL or use file path
                # Note: This depends on the specific API requirements
                input_params["start_image"] = start_image_path
                logger.info(f"Using start image: {start_image_path}")

            # Add end image if available
            if end_image_path:
                input_params["end_image"] = end_image_path
                logger.info(f"Using end image: {end_image_path}")

            logger.info(f"Generating 5-second video for segment {visual_segment.segment_number}")
            logger.debug(f"Input params: {input_params}")

            # Generate video using Replicate
            video_url = replicate.run(
                "bytedance/seedance-1-lite",
                input=input_params
            )

            logger.debug(f"Video generated, downloading from: {video_url}")

            # Download the video file
            response = requests.get(video_url, stream=True, timeout=300)  # 5 minute timeout
            response.raise_for_status()

            # Save to temporary file first
            temp_output_path = output_path.replace('.mp4', '_temp.mp4')
            with open(temp_output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            logger.info(f"5-second video saved to: {temp_output_path}")

            # Calculate the actual target duration based on STT timing analysis
            target_duration = self._calculate_target_duration(visual_segment, short_dir)

            if target_duration > 0 and target_duration != 5:
                # Adjust video speed to match target duration
                if self._adjust_video_speed(temp_output_path, output_path, target_duration):
                    # Remove temporary file
                    os.remove(temp_output_path)
                    logger.info(f"Speed-adjusted video saved to: {output_path}")
                else:
                    # If speed adjustment fails, use the original 5-second video
                    os.rename(temp_output_path, output_path)
                    logger.warning(f"Speed adjustment failed, using 5-second video: {output_path}")
            else:
                # No speed adjustment needed (either target_duration is 0 or equals 5)
                os.rename(temp_output_path, output_path)
                if target_duration == 0:
                    logger.warning(f"No timing data available, using 5-second video: {output_path}")
                else:
                    logger.info(f"Video saved to: {output_path}")

            return True

        except Exception as e:
            logger.error(f"Error generating video for segment {visual_segment.segment_number}: {str(e)}")
            return False

    def _calculate_target_duration(self, visual_segment: VisualSegment, short_dir: str) -> float:
        """
        Calculate target duration based on actual STT timing using Deepgram analysis.

        Args:
            visual_segment (VisualSegment): The visual segment
            short_dir (str): Path to the short directory containing audio files

        Returns:
            float: Target duration in seconds (0.0 if no audio found or analysis fails)
        """
        if not self.deepgram_client:
            logger.warning("Deepgram client not available")
            return 0.0

        # Look for existing segment audio file
        audio_dir = os.path.join(short_dir, "audio")
        segment_audio_path = os.path.join(audio_dir, f"short_{os.path.basename(short_dir).split('_')[-1]}_segment_{visual_segment.segment_number}.mp3")

        if not os.path.exists(segment_audio_path):
            logger.warning(f"Segment audio file not found: {segment_audio_path}")
            return 0.0

        try:
            actual_duration = self.deepgram_client.calculate_narration_duration(
                segment_audio_path,
                visual_segment.narration
            )
            logger.info(f"STT calculated duration: {actual_duration:.2f}s for segment {visual_segment.segment_number}")
            return actual_duration

        except Exception as e:
            logger.error(f"Error calculating duration with Deepgram: {str(e)}")
            return 0.0

    def _adjust_video_speed(self, input_path: str, output_path: str, target_duration: float) -> bool:
        """
        Adjust video speed to match target duration using moviepy.

        Args:
            input_path (str): Path to the input 5-second video
            output_path (str): Path to save the speed-adjusted video
            target_duration (float): Target duration in seconds

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Load the video
            clip = VideoFileClip(input_path)

            # Calculate speed factor (5 seconds / target_duration)
            speed_factor = 5.0 / target_duration

            logger.info(f"Adjusting video speed by factor {speed_factor:.2f} (5s -> {target_duration:.1f}s)")

            # Adjust speed
            adjusted_clip = clip.fx(lambda clip: clip.speedx(speed_factor))

            # Write the adjusted video
            adjusted_clip.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )

            # Clean up
            clip.close()
            adjusted_clip.close()

            return True

        except Exception as e:
            logger.error(f"Error adjusting video speed: {str(e)}")
            return False

    def process_short_segments(self, short_dir: str) -> List[str]:
        """
        Process all segments in a short directory to generate videos using two-phase approach.

        Phase 1: Visual Planning (if not already done)
        Phase 2: Video Generation

        Args:
            short_dir (str): Path to the short directory

        Returns:
            List[str]: List of generated video file paths
        """
        short_name = os.path.basename(short_dir)

        # Phase 1: Visual Planning
        visual_plan = self.visual_planner.load_visual_plan(short_dir)

        if visual_plan is None:
            logger.info(f"Creating visual plan for {short_name}")

            if not self.visual_planner.process_short(short_dir):
                logger.error(f"Failed to create visual plan for {short_name}")
                return []

            visual_plan = self.visual_planner.load_visual_plan(short_dir)

            if visual_plan is None:
                logger.error(f"Could not load visual plan after creation for {short_name}")
                return []

        else:
            logger.info(f"Using existing visual plan for {short_name}")

        # Phase 2: Video Generation
        # Create video directory if it doesn't exist
        video_dir = os.path.join(short_dir, 'video')
        os.makedirs(video_dir, exist_ok=True)

        generated_videos = []

        for visual_segment in visual_plan.segments:
            # Generate filename matching the audio file pattern
            clean_short_name = short_name.replace("-", "_")
            video_filename = f"{clean_short_name}_segment_{visual_segment.segment_number}.mp4"
            video_path = os.path.join(video_dir, video_filename)

            # Skip if video already exists (for --continue functionality)
            if os.path.exists(video_path):
                logger.info(f"Video already exists, skipping: {video_path}")
                generated_videos.append(video_path)
                continue

            # Generate video using the planned visual prompt with frame control
            if self.generate_video(visual_segment, video_path, short_dir):
                generated_videos.append(video_path)
            else:
                logger.warning(f"Failed to generate video for segment {visual_segment.segment_number}")

        logger.info(f"Generated {len(generated_videos)} videos for {short_name}")
        return generated_videos

    def process_all_shorts(self, story_dir: str) -> Dict[str, List[str]]:
        """
        Process all shorts in the story directory to generate videos.

        Args:
            story_dir (str): Path to the story directory

        Returns:
            Dict[str, List[str]]: Dictionary mapping short names to lists of generated video paths
        """
        short_dirs = get_shorts_directories(story_dir)
        if not short_dirs:
            logger.warning(f"No shorts directories found in {story_dir}")
            return {}

        all_generated_videos = {}

        for short_dir in short_dirs:
            short_name = os.path.basename(short_dir)
            logger.info(f"Processing videos for {short_name}")

            try:
                generated_videos = self.process_short_segments(short_dir)
                all_generated_videos[short_name] = generated_videos
                
            except Exception as e:
                logger.error(f"Error processing {short_name}: {str(e)}")
                all_generated_videos[short_name] = []

        total_videos = sum(len(videos) for videos in all_generated_videos.values())
        logger.info(f"Video generation completed. Generated {total_videos} videos across {len(short_dirs)} shorts")

        return all_generated_videos
